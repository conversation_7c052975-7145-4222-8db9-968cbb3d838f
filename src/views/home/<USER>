<template>
  <el-container class="page-container" v-loading=home_loading>
    <!-- 首页内容 -->
    <transition name="slide-fade">
      <div v-show="showHomePage" class="portal-home" >
        <!-- 欢迎语和交互提示 -->
        <div class="welcome-section">
          <div class="title-container">
            <h1 class="welcome-title">
              {{ welcomeMessage }}
            </h1>
            <div class="title-underline"></div>
          </div>
          <p class="welcome-subtitle">{{ interactionTip }}</p>
          <div class="floating-icons">
            <div class="floating-icon" v-for="i in 6" :key="i" :style="getFloatingIconStyle(i)">
              <i :class="getRandomIcon()"></i>
            </div>
          </div>
        </div>

        <!-- 交互对话框 -->
        <div class="chat-section">
          <div class="chat-container">
            <chat-input-box @send-message="handleSendMessage" @upload-file="handleUploadFile" />
          </div>
        </div>

        <!-- 推荐应用 -->
        <div class="recommended-apps">
          <div class="section-title">
            <h3>推荐应用</h3>
            <div class="title-decoration"></div>
          </div>
          <div class="apps-buttons">
            <el-tooltip
              v-for="(app, index) in recommendedApps"
              :key="app.id"
              class="item"
              :content="formatDescription(app.description)"
              placement="top"
              effect="dark"
              raw-content
            >
              <div
                class="app-button"
                @click="handleAppClick(app)"
                :style="{ animationDelay: index * 0.1 + 's' }"
              >
                <div class="app-icon-wrapper">
                  <div class="app-icon">
                    <i :class="app.iconClass" v-if="app.iconClass"></i>
                    <span v-else>{{ app.icon }}</span>
                  </div>
                  <div class="icon-glow"></div>
                </div>
                <span class="app-name">{{ app.name }}</span>
                <span v-if="app.badge" class="app-badge">{{ app.badge }}</span>
                <div class="app-ripple"></div>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </transition>

    <!-- IframeCom 组件 -->
    <transition name="fade">
      <div v-show="!showHomePage" class="iframe-container">
        <IframeCom ref="iframeCom" />
      </div>
    </transition>
  </el-container>
</template>

<script>
import ChatInputBox from "./ChatInputBox/index.vue";
import IframeCom from "../difyIframe/index.vue";
import { getWelcomeData, getRecommendedApps, sendChatMessage, uploadChatFile } from "@/api/portal";

export default {
  name: "PortalHome",
  components: {
    ChatInputBox,
    IframeCom
  },
  data() {
    return {
      welcomeMessage: "欢迎进入AI门户系统",
      interactionTip: "我可以帮您完成各种图文任务",
      loading: false,
      showHomePage: true,
      authToken: {},
      url: '',
      home_loading: true,
      floatingIcons: [
        'el-icon-star-on', 'el-icon-magic-stick', 'el-icon-cpu',
        'el-icon-brush', 'el-icon-edit', 'el-icon-document'
      ],
      recommendedApps: [
        {
          id: 1,
          name: "代码解读\r\n(10.0网段)",
          icon: "📊",
          iconClass: "el-icon-document",
          link: "http://**********:13000/wiki0/console",
          badge: "新",
          description: "自动分析代码仓库，生成可视化文档\r\n(10.0网段Gitlab)"
        },
        {
          id: 2,
          name: "DeepWiki1",
          icon: "📊",
          iconClass: "el-icon-document",
          link: "http://**********:13000/wiki1/console",
          badge: "新",
          description: "自动分析代码仓库，生成可视化文档(10.1网段Gitlab)"
        },
        {
          id: 3,
          name: "数字人",
          icon: "🤖",
          iconClass: "el-icon-service",
          link: "DigitalPage",
          badge: "新",
          description: "可以与您语音交流产品的数字人"
        },
        {
          id: 4,
          name: "编码\n助手",
          icon: "💻",
          iconClass: "el-icon-cpu",
          link: "",
          appId: "bc940590-5d0a-4bf3-be4a-3e5fdf7851ee",
          description: "智能代码生成\n和优化助手"
        },
        {
          id: 5,
          name: "UI 设计",
          icon: "🎨",
          iconClass: "el-icon-brush",
          link: "",
          appId: "7322a6e9-b3ae-4cea-89df-614e9a06d2d5",
          description: "快速创建和编辑用户界面设计"
        },
        {
          id: 6,
          name: "公式\n编辑",
          icon: "ƒ",
          iconClass: "el-icon-edit",
          link: "",
          appId: "3a0e1add-d349-4f97-abbc-4a4d277cf7ea",
          description: "latex公式编辑\n与识别"
        },
        {
          id: 7,
          name: "更多",
          icon: "⋯",
          iconClass: "el-icon-more",
          link: "apps",
          description: "查看更多应用"
        }
      ]
    };
  },
  mounted() {
    this.initPageData();
    window.addEventListener('message', this.handleMessageEvent);
  },
  methods: {
    async initPageData() {
      await Promise.all([
        // this.fetchWelcomeData(),
        // this.fetchRecommendedApps()
      ]);
    },



    // 获取浮动图标样式
    getFloatingIconStyle(index) {
      const positions = [
        { top: '20%', left: '10%' },
        { top: '30%', right: '15%' },
        { top: '60%', left: '5%' },
        { top: '70%', right: '10%' },
        { top: '40%', left: '15%' },
        { top: '50%', right: '20%' }
      ];

      const position = positions[index - 1] || positions[0];
      const animationDelay = index * 0.5;

      return {
        ...position,
        animationDelay: `${animationDelay}s`
      };
    },

    // 获取随机图标
    getRandomIcon() {
      return this.floatingIcons[Math.floor(Math.random() * this.floatingIcons.length)];
    },

    // 格式化描述文本，支持换行
    formatDescription(description) {
      if (!description) return '';
      // 将换行符转换为HTML的<br>标签
      return description.replace(/\n/g, '<br>').replace(/\r\n/g, '<br>').replace(/\r/g, '<br>');
    },

    async handleSendMessage(messageData) {
      try {
        this.loading = true;
        console.log("发送消息:", messageData);

        // 切换到iframe页面
        this.showHomePage = false;

        // 等待DOM更新后，向IframeCom传送数据
        this.$nextTick(() => {
          if (this.$refs.iframeCom) {
            this.$refs.iframeCom.sendMessageToIframe(messageData);
          }
        });

      } catch (error) {
        console.error("发送消息失败:", error);
        this.$message.error("发送消息失败，请重试");
        // 如果发送失败，回到首页
        this.showHomePage = true;
      } finally {
        this.loading = false;
      }
    },

    async handleUploadFile(file, callback) {
      try {
        console.log("上传文件:", file);

        const formData = new FormData();
        formData.append('file', file);

        const response = await uploadChatFile(formData, this.authToken);
        console.log("文件上传成功:", response);
        this.$message.success(`文件 ${file.name} 上传成功`);
        callback(response);  // 调用回调函数处理上传结果
      } catch (error) {
        console.error("文件上传失败:", error);
        this.$message.error(`文件 ${file.name} 上传失败，请重试`);
      }
    },

    handleAppClick(app) {
      // console.log("点击应用:", app);
      if (app.link) {
        // 跳转到对应链接
        if (app.link.startsWith('http')) {
          // 外部链接新窗口打开
          window.open(app.link, '_blank');
        } else {
          // 内部路由跳转
          this.$router.push({
              name: app.link
          });
        }
      } else {
        this.$router.push({
          name: "NewChatPage",
          query: { appId: app.appId },
        });
      }
    },

    async fetchWelcomeData() {
      try {
        const response = await getWelcomeData();
        this.welcomeMessage = response.data.welcomeMessage || "欢迎进入XXX";
        this.interactionTip = response.data.interactionTip || "我可以帮你完成";
      } catch (error) {
        console.error("获取欢迎信息失败:", error);
        // 使用默认值，不显示错误信息
      }
    },

    async fetchRecommendedApps() {
      try {
        const response = await getRecommendedApps();
        if (response.data && response.data.length > 0) {
          this.recommendedApps = response.data.slice(0, 4); // 最多显示4个
        }
      } catch (error) {
        console.error("获取推荐应用失败:", error);
        // 使用默认数据，不显示错误信息
      }
    },

    // 消息接收处理
    handleMessageEvent(event) {
      if (event.data?.type === 'dify-chatbot-response') {
        console.log(`收到Chatbot响应: ${JSON.stringify(event.data.payload)}`);
      } else if (event.data?.type === 'auth-token') {
        this.url = this.$refs.iframeCom.url;
        let authTokens = event.data.token
        authTokens = JSON.parse(authTokens)
        // 获取对应token
        const pathPart = this.url.split('?')[0];     
        const appId = pathPart.split('/')[2];  
        const tokenObj = authTokens[appId]
        // 获取第一个 key 对应的 value
        this.authToken = tokenObj ? Object.values(tokenObj)[0] : null;
        // console.log(`收到认证令牌: ${this.authToken}，应用url: ${this.url}`);
      } else if (event.data?.type === 'dify-chatbot-iframe-ready') {
        this.home_loading = false;
      } else {
        console.log(`收到未知类型消息: ${event.data?.type}`);
      }
    },
  },

  beforeDestroy() {
    window.removeEventListener('message', this.handleMessageEvent);
  }
  
};
</script>

<style scoped lang="scss">
.page-container {
  height: 100%;
  background: transparent;
  position: relative;
  overflow: hidden;
}

.portal-home {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // 透明背景
  // background: transparent;
  // 白色背景
  background: #fff;
  // 渐变色背景
  // background: linear-gradient(135deg,
  //             rgba(100, 181, 246, 0.1) 0%,
  //             rgba(255, 255, 255, 0.95) 25%,
  //             rgba(255, 255, 255, 0.98) 50%,
  //             rgba(255, 255, 255, 0.95) 75%,
  //             rgba(66, 165, 245, 0.1) 100%);
  // 背景通过动态样式设置
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}

.iframe-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  ::v-deep .dify-iframe,
  ::v-deep iframe {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    display: block !important;
  }
}

// 过渡动画
.fade-enter-active, .fade-leave-active {
  transition: opacity 1s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.slide-fade-enter-from {
  transform: translateY(50px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(-50px);
  opacity: 0;
}

.welcome-section {
  text-align: center;
  max-width: 800px;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  height: 33.33vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.title-container {
  position: relative;
  margin-bottom: 30px;
}

.welcome-title {
  font-size: clamp(28px, 5vw, 48px);
  font-weight: 700;
  margin: 0 0 1vh 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 50%, #64b5f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

.title-underline {
  height: 4px;
  background: linear-gradient(90deg, transparent 0%, #64b5f6 50%, transparent 100%);
  margin: 0 auto;
  width: 0;
  animation: underlineExpand 2s ease-out 1s forwards;
  border-radius: 2px;
}

@keyframes underlineExpand {
  to {
    width: 200px;
  }
}

.welcome-subtitle {
  font-size: clamp(16px, 3vw, 20px);
  color: #546e7a;
  margin: 0;
  line-height: 1.5;
  text-shadow: none;
  animation: subtitleFadeIn 1s ease-out 0.5s both;
}

@keyframes subtitleFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;

  .floating-icon {
    position: absolute;
    font-size: 24px;
    color: rgba(100, 181, 246, 0.4);
    animation: floatIcon 6s ease-in-out infinite;

    i {
      filter: drop-shadow(0 2px 4px rgba(100, 181, 246, 0.3));
    }
  }
}

@keyframes floatIcon {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.7;
  }
}

.chat-section {
  width: 100%;
  max-width: 800px;
  margin-bottom: 3vh;
  display: flex;
  justify-content: center;
  z-index: 2;
  position: relative;
  flex-shrink: 0;
}

.chat-container {
  width: 100%;
  background: rgba(100, 181, 246, 0.12);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(100, 181, 246, 0.2);
  box-shadow: 0 8px 32px rgba(100, 181, 246, 0.15);
  animation: chatContainerSlideUp 1s ease-out 1s both;
}

@keyframes chatContainerSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recommended-apps {
  width: 100%;
  max-width: 800px;
  margin-top: 2vh;
  z-index: 2;
  position: relative;
  flex-shrink: 0;
}

.section-title {
  text-align: center;
  margin-bottom: 30px;

  h3 {
    font-size: 24px;
    color: #1976d2;
    margin: 0 0 10px 0;
    font-weight: 600;
    text-shadow: none;
  }

  .title-decoration {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #64b5f6, #42a5f5);
    margin: 0 auto;
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(100, 181, 246, 0.3);
  }
}

.apps-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: clamp(12px, 3vw, 24px);
  flex-wrap: wrap;
  max-width: 100%;
}

.app-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  background: rgba(100, 181, 246, 0.08);
  border: 1px solid rgba(100, 181, 246, 0.2);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-width: 90px;
  position: relative;
  overflow: hidden;
  animation: appButtonSlideIn 0.6s ease-out both;

  &:hover {
    background: rgba(100, 181, 246, 0.15);
    border-color: rgba(100, 181, 246, 0.5);
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 35px rgba(100, 181, 246, 0.2),
                0 5px 15px rgba(100, 181, 246, 0.3);

    .app-icon-wrapper {
      transform: scale(1.1);

      .icon-glow {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    .app-ripple {
      animation: ripple 0.6s ease-out;
    }
  }

  &:active {
    transform: translateY(-4px) scale(1.02);
  }
}

@keyframes appButtonSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.app-icon-wrapper {
  position: relative;
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.app-icon {
  font-size: 24px;
  color: #1976d2;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 2px 4px rgba(25, 118, 210, 0.2));

  i {
    font-size: 24px;
  }
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, rgba(100, 181, 246, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1;
}

.app-name {
  font-size: 14px;
  color: #37474f;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
  text-shadow: none;
  white-space: pre-line; /* 支持换行符显示 */
}

.app-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  color: white;
  font-size: 10px;
  padding: 3px 8px;
  border-radius: 12px;
  line-height: 1;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.app-ripple {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 16px;
  opacity: 0;
}

@keyframes ripple {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .portal-home {
    padding: 15px 10px;
  }

  .welcome-section {
    margin-bottom: 1.5vh;
  }

  .chat-section {
    margin-bottom: 2vh;
  }

  .chat-container {
    padding: 15px;
    border-radius: 15px;
  }

  .recommended-apps {
    margin-top: 1.5vh;
  }

  .section-title h3 {
    font-size: 20px;
  }

  .apps-buttons {
    gap: 16px;
  }

  .app-button {
    min-width: 75px;
    padding: 16px 12px;
  }

  .app-icon {
    font-size: 20px;
    margin-bottom: 8px;

    i {
      font-size: 20px;
    }
  }

  .app-name {
    font-size: 13px;
  }

  .floating-icons .floating-icon {
    font-size: 20px;
  }

  .gradient-orbs .orb {
    &.orb-1 {
      width: 200px;
      height: 200px;
    }

    &.orb-2 {
      width: 150px;
      height: 150px;
    }

    &.orb-3 {
      width: 180px;
      height: 180px;
    }
  }
}

@media (max-width: 480px) {
  .portal-home {
    padding: 10px 8px;
  }

  .welcome-section {
    margin-bottom: 1vh;
  }

  .chat-section {
    margin-bottom: 1.5vh;
  }

  .recommended-apps {
    margin-top: 1vh;
  }

  .chat-container {
    padding: 12px;
    border-radius: 12px;
  }

  .section-title h3 {
    font-size: 18px;
    margin-bottom: 1vh;
  }

  .app-button {
    min-width: 65px;
    padding: 12px 8px;
    border-radius: 12px;
  }

  .app-icon {
    font-size: 18px;
    margin-bottom: 6px;

    i {
      font-size: 18px;
    }
  }

  .app-name {
    font-size: 12px;
  }

  .floating-icons .floating-icon {
    font-size: 16px;
  }

  .apps-buttons {
    gap: 12px;
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .page-container {
    background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
  }

  .welcome-title {
    background: linear-gradient(135deg, #ffffff 0%, #e1f5fe 50%, #b3e5fc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
</style>
